# Run this with: poetry run python canjank.py
import can
import time
import signal
import sys
import select
import termios
import tty
import struct
import numpy as np


def send_command_and_wait(command, source_id, device_id, bus, 
                          timeout=1.0, require_ok=True, wait_after_ok_ms=0):
    """
    Sends a command to a moteus controller and waits for a response.
    
    Args:
        command: String command to send (e.g. "conf set servo.max_velocity 300.0")
        source_id: Source ID (your controller ID)
        device_id: Target device ID (moteus controller ID)
        bus: python-can bus instance
        timeout: Maximum time to wait for response in seconds
        require_ok: If True, wait for "OK" response, otherwise return after any response
        wait_after_ok_ms: Additional delay after receiving OK (milliseconds)
        
    Returns:
        (success, response_text) tuple where:
        - success is a boolean indicating if "OK" was received
        - response_text contains the complete response text
    """
    # 1. Add CR+LF to beginning and end of command
    modified_command = "\r\n" + command + "\r\n"
    
    # 2. Create the data payload with the modified command
    data = [0x40, 0x01, len(modified_command)] + [ord(c) for c in modified_command]
    
    # 3. Pad to a valid CAN-FD length if necessary
    valid_lengths = [0, 1, 2, 3, 4, 5, 6, 7, 8, 12, 16, 20, 24, 32, 48, 64]
    data_len = len(data)
    
    # Find the next valid length if current length is not valid
    if data_len not in valid_lengths:
        next_valid_length = next(length for length in valid_lengths if length > data_len)
        # Pad with 0x50 bytes
        padding_needed = next_valid_length - data_len
        data.extend([0x50] * padding_needed)
    
    # 4. Send the command
    msg = can.Message(
        arbitration_id=(source_id << 8) | device_id | 0x8000,  # Set highest bit for reply
        data=data,
        is_extended_id=True,
        is_fd=True,
        bitrate_switch=True
    )
    bus.send(msg)

    # time.sleep(0.01)

    # 5. Wait for and accumulate the response using polling
    start_time = time.time()
    accumulated_data = ""
    
    while time.time() - start_time < timeout:
        # Send poll request
        poll_msg = can.Message(
            arbitration_id=(source_id << 8) | device_id | 0x8000,  # Set highest bit for reply
            data=[0x42, 0x01, 0x30],  # Poll command with max length 48 bytes
            is_extended_id=True,
            is_fd=True,
            bitrate_switch=True
        )
        bus.send(poll_msg)
        
        # Wait for response
        msg = bus.recv(timeout=0.05)
        
        # Check if we got a message
        if msg is None:
            continue
            
        # Check if this is a response to our message from the correct device
        expected_arb_id = (device_id << 8) | source_id
        if (msg.arbitration_id & 0xFFFF) != expected_arb_id:
            continue
            
        # Check if this is a tunneling response (0x41 is server-to-client)
        if len(msg.data) >= 3 and msg.data[0] == 0x41 and msg.data[1] == 0x01:
            # Get the length of data
            data_len = msg.data[2]
            
            # If there's no data, continue polling
            if data_len == 0:
                continue
                
            # Extract the ASCII data
            ascii_data = bytes(msg.data[3:3+data_len]).decode('ascii')
            accumulated_data += ascii_data
            
            # Check if we have a complete response
            if require_ok and "OK" in accumulated_data:
                if wait_after_ok_ms > 0:
                    time.sleep(wait_after_ok_ms / 1000.0)
                return True, accumulated_data
            elif not require_ok and len(accumulated_data) > 0:
                return True, accumulated_data
        
        # Short delay between polls to avoid overwhelming the bus
        # time.sleep(0.001)
    
    # Timeout expired without finding expected response
    return False, accumulated_data


# Pause for 0.5 seconds at the beginning
# time.sleep(0.5)

# Flag to indicate we're shutting down
running = True

# Signal handler for clean shutdown
def signal_handler(sig, frame):
    global running
    print("\nCaught signal, shutting down...")
    running = False

# Register signal handlers
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

# Dictionary to hold all bus instances
buses = {}  # Will contain {1: bus1, 2: bus2, 3: bus3, 4: bus4}

# Configure with CAN-FD support
try:
    # Get unique CAN channels from devices array
    unique_channels = np.unique(devices[:, 0]).astype(int)
    
    # Set up CAN buses for each channel
    for channel_num in unique_channels:
        channel_name = f'can{channel_num}'
        print(f"Setting up {channel_name}...")
        
        bus = can.interface.Bus(
            channel=channel_name,
            interface='socketcan',
            fd=True,  # Enable CAN-FD support
            bitrate=1000000,
            data_bitrate=5000000,
            # Add timing parameters based on Moteus docs for 80MHz clock
            sjw=10,
            dsjw=5,
            sample_point=0.666,
            dsample_point=0.666,
            receive_own_messages=False
        )
        
        # Try to disable auto-retransmission if the bus supports it
        try:
            if hasattr(bus, 'set_auto_retransmit'):
                bus.set_auto_retransmit(False)
        except:
            print(f"Note: Could not disable auto-retransmission for {channel_name}")

        # Clear the receive buffer of any stale messages
        print(f"Clearing RX buffer for {channel_name}...")
        while bus.recv(timeout=0) is not None:
            pass
        print(f"RX buffer cleared for {channel_name}.")
        
        buses[channel_num] = bus
    
    print(f"Initialized {len(buses)} CAN buses: {list(buses.keys())}")

    print("Ready. Press '1' to request temperature, '2' to stop motor, '3' to send velocity command, '4' to send velocity command in opposite direction, '5' to request position, '6' to request aux1 encoder and quaternion, 'q' to quit.")

    # Enable non-blocking keyboard input
    fd = sys.stdin.fileno()
    old_term_settings = termios.tcgetattr(fd)
    tty.setcbreak(fd)

    # Device configuration array: [bus_num, device_id]
    # Format: each row is [bus_number, device_id]
    # eventual format should be: whichCan, addr, minAngle, maxAngle, magnetOffset, x, y
    # dynamic: angle, active, tempC, quatX, quatY, quatZ
    devices = np.array([
        [4, 54, 0.0, 0.0, 0.0, 0, 0],    # right pelvis
        [4, 18, 0.0, 0.0, 0.0, 0, 0],    # right butt
        [4, 38, 0.0, 0.0, 0.0, 0, 0],    # right femur twist
        [4, 20, 0.0, 0.0, 0.0, 0, 0],    # right knee
        [4, 50, 0.0, 0.0, 0.0, 0, 0],    # right shin twist
        [4, 24, 0.0, 0.0, 0.0, 0, 0],    # right ankle roll
        [4, 32, 0.0, 0.0, 0.0, 0, 0],    # right ankle pitch

        [3, 10, 0.0, 0.0, 0.0, 0, 0],    # left pelvis
        [3, 30, 0.0, 0.0, 0.0, 0, 0],    # left butt
        [3, 46, 0.0, 0.0, 0.0, 0, 0],    # left femur twist
        [3, 52, 0.0, 0.0, 0.0, 0, 0],    # left knee
        [3, 34, 0.0, 0.0, 0.0, 0, 0],    # left shin twist
        [3, 56, 0.0, 0.0, 0.0, 0, 0],    # left ankle roll
        [3, 40, 0.0, 0.0, 0.0, 0, 0],    # left ankle pitch

        [2, 26, 0.0, 0.0, 0.0, 0, 0],    # spine twist
        [2, 44, 0.0, 0.0, 0.0, 0, 0],    # chest lean
        [2, 42, 0.0, 0.0, 0.0, 0, 0],    # right inner shoulder
        [2, 28, 0.0, 0.0, 0.0, 0, 0],    # right outer shoulder
        [2, 22, 0.0, 0.0, 0.0, 0, 0],    # right bicep twist
        [2, 48, 0.0, 0.0, 0.0, 0, 0],    # right elbow

        [1, 16, 0.0, 0.0, 0.0, 0, 0],    # left inner shoulder
        [1, 14, 0.0, 0.0, 0.0, 0, 0],    # left outer shoulder
        [1, 12, 0.0, 0.0, 0.0, 0, 0],    # left bicep twist
        [1, 36, 0.0, 0.0, 0.0, 0, 0],    # left elbow

    ])

    # Current device selection (can be changed to test different devices)
    current_device_idx = 3  # right knee (device_id 20)
    device_id = int(devices[current_device_idx, 1])  # Get device_id from array
    can_channel = int(devices[current_device_idx, 0])  # Get CAN channel from array
    current_bus = buses[can_channel]  # Get the appropriate bus
    source_id = 0
    broadcast = False




    # command = "conf get servo.max_velocity"
    
    # # Send the command and wait for response
    # success, response = send_command_and_wait(command, source_id, device_id, current_bus, timeout=1.0, require_ok=False)
    
    # if not success:
    #     print(f"Failed to get max_velocity: {response}")
    #     exit(1)
    
    # # Parse the response to extract the value
    # # Response format will be something like "500.0\r\nOK\r\n"
    # lines = response.strip().split('\r\n')
    # try:
    #     # The value should be in the first line
    #     value = float(lines[0])
    #     print(f"Max velocity: {value}")
    # except ValueError:
    #     print(f"Failed to parse max_velocity response: {response}")
    #     exit(1)





    # # Init config values
    # # Step 1: Set the parameter
    # success, response = send_command_and_wait(
    #     f"conf set servo.max_velocity 51.2",
    #     source_id, device_id, current_bus
    # )
    
    # if not success:
    #     print(f"Failed to set max_velocity: {response}")
    #     exit(1)

    # # Step 2: Save the configuration
    # success, response = send_command_and_wait(
    #     "conf write", 
    #     source_id, device_id, current_bus, 
    #     wait_after_ok_ms=50  # Add a delay after writing configuration
    # )
    
    # if not success:
    #     print(f"Failed to save configuration: {response}")
    #     exit(1)


    while running:
        # Check for keyboard input (non-blocking)
        if select.select([sys.stdin], [], [], 0)[0]:
            ch = sys.stdin.read(1)
            if ch == '1':
                # Build and send a temperature request frame
                msg = can.Message(
                    arbitration_id=(source_id << 8) | (device_id if not broadcast else 0) | 0x8000,
                    data=[0x15, 0x0e],
                    is_extended_id=True,
                    is_fd=True,
                    bitrate_switch=True
                )
                current_bus.send(msg)
                print("\nTemperature request sent.")
            elif ch == '2':
                # Build and send a stop command
                # Command 0x01 = Mode, value 0 = Stopped/disabled
                msg = can.Message(
                    arbitration_id=(source_id << 8) | (device_id if not broadcast else 0) | 0x8000,
                    data=[0x01, 0x00, 0x00],
                    is_extended_id=True,
                    is_fd=True,
                    bitrate_switch=True
                )
                current_bus.send(msg)
                print("\nStop command sent.")
            elif ch == '3':
                # Build and send a velocity command (slow rotation)
                # Format: Set mode to position (0x0a), set position to current position (0x8000),
                # set velocity to moderate rotation
                msg = can.Message(
                    arbitration_id=(source_id << 8) | (device_id if not broadcast else 0) | 0x8000,
                    data=[
                    #     # 0x01, 0x00, 0x0a,        # Set mode to position (10)
                    #     # 0x06, 0x20,              # Write 2 int16 registers starting at 0x020
                    #     # 0x10, 0x27,              # Position = 0x8000 (special: use current position)
                    #     # 0x00, 0x00,              # Velocity = 0x0960 = 2400 = 0.6 Hz (~20 RPM at output)
                    #     # 0x01, 0x27, 0x80         # Set watchdog timeout (0x80 = disable timeout)

                    #     # 0x01, 0x00, 0x0a,        # Set mode to position (10)
                    #     # 0x06, 0x20,              # Write 2 int16 registers starting at 0x020
                    #     # 0x00, 0x80,              # Position = 0x8000 (special: use current position)
                    #     # 0xc7, 0x01,              # Velocity = 0x0960 = 2400 = 0.6 Hz (~20 RPM at output)
                    #     # 0x05, 0x25,              # Write 1 int16 register at 0x025
                    #     # 0x1e, 0x00,              # Max torque = 0x001e = 30 = 0.3 Nm
                    #     # 0x01, 0x27, 0x80         # Set watchdog timeout (0x80 = disable timeout)

                    # 0x01, 0x00, 0x0a,
                    # 0x0a, 0x20,
                    # 0x00, 0x00, 0x00, 0x80,     # Position = 0x8000 (special: use current position)
                    # 0x00, 0x01, 0x21, 0x00,
                    # 0x01, 0x27, 0x80

                        0x01, 0x00, 0x0a,        # Set mode to position (10)
                        0x02, 0x20,              # Write 1 int8 register starting at 0x020 (position command)
                        0x80,                    # Position = 0x80 = 128 = 1.28 revolutions
                        0x02,                    # Velocity = 0x18 = 24 = 0.24 Hz
                        0x01, 0x27, 0x80         # Write 1 int8 register at 0x027: watchdog timeout = 0x80 (disable timeout)
                    ],

                    # data=bytearray([
                    #     0x01, 0x00, 0x0a,        # Set mode to position (10)
                    #     0x0c,                    # Write float (base command, no count in LSBs)
                    #     0x06,                    # varuint: 6 registers
                    #     0x20,                    # varuint: starting register 0x020
                    # ]) + 
                    # # struct.pack('<f', 18.0) +  # Position = NaN
                    # # struct.pack('<f', 10.5) +            # Velocity = 0.5 Hz
                    # # struct.pack('<f', 0.0) +            # Feedforward torque = 0
                    # # struct.pack('<f', 1.0) +            # Kp scale = 1.0
                    # # struct.pack('<f', 1.0) +            # Kd scale = 1.0  
                    # # struct.pack('<f', 0.3) +            # Max torque = 0.3 Nm

                    # # struct.pack('<f', float('nan')) +  # Position = NaN
                    # # struct.pack('<f', 20.5) +            # Velocity = 0.5 Hz
                    # # struct.pack('<f', 0.0) +            # Feedforward torque = 0
                    # # struct.pack('<f', 1.0) +            # Kp scale = 1.0
                    # # struct.pack('<f', 1.0) +            # Kd scale = 1.0  
                    # # struct.pack('<f', 0.3) +            # Max torque = 0.3 Nm
                    # bytearray([
                    #     0x01, 0x27, 0x80         # Timeout
                    # ]),



                    is_extended_id=True,
                    is_fd=True,
                    bitrate_switch=True
                )
                current_bus.send(msg)
                print("\nVelocity command sent - moderate rotation with no timeout.")
            elif ch == '4':
                # Build and send a velocity command (rotation in opposite direction)
                # Format: Set mode to position (0x0a), set position to current position (0x8000),
                # set velocity to moderate negative rotation
                msg = can.Message(
                    arbitration_id=(source_id << 8) | (device_id if not broadcast else 0) | 0x8000,
                    data=[
                        0x01, 0x00, 0x0a,        # Set mode to position (10)
                        0x07, 0x20,              # Write 3 int16 registers starting at 0x020
                        0x00, 0x80,              # Position = 0x8000 (special: use current position)
                        0xa0, 0xfc,              # Velocity = 0xF6A0 = -2400 = -0.6 Hz (~-20 RPM at output)
                        0x00, 0x00,              # Feedforward torque = 0
                        0x01, 0x27, 0x80         # Set watchdog timeout (0x80 = disable timeout)
                    ],
                    is_extended_id=True,
                    is_fd=True,
                    bitrate_switch=True
                )
                current_bus.send(msg)
                print("\nVelocity command sent - moderate rotation in opposite direction with no timeout.")
            elif ch == '5':
                # Build and send a position request frame
                # Read register 0x001 (position) as int16
                msg = can.Message(
                    arbitration_id=(source_id << 8) | (device_id if not broadcast else 0) | 0x8000,
                    data=[0x14, 0x01, 0x01],  # Read 1 int16 register starting at 0x001 (position)
                    is_extended_id=True,
                    is_fd=True,
                    bitrate_switch=True
                )
                current_bus.send(msg)
                print("\nPosition request sent.")
            elif ch == '6':
                # Build and send a request for aux1 encoder and quaternion values
                # Read register 0x050 (Aux1 encoder position) and 0x072-0x074 (quaternion X,Y,Z) as int16
                msg = can.Message(
                    arbitration_id=(source_id << 8) | (device_id if not broadcast else 0) | 0x8000,
                    data=[
                        0x14 + 1, 0x50,  # Read 1 int16 register at 0x050 (Aux1 encoder position)
                        0x14 + 3, 0x72,  # Read 3 int16 registers starting at 0x072 (quaternion X,Y,Z)
                        0x1c + 1, 0x01,  # Read float32 position.
                        0x10 + 1, 0x0e,  # Read byte board temp.
                        # 0x10 + 1, 0x0a,  # Read byte motor temp.
                    ],
                    is_extended_id=True,
                    is_fd=True,
                    bitrate_switch=True
                )
                current_bus.send(msg)
                print("\nAux1 encoder and quaternion request sent.")
            elif ch == 'q':
                print("\nQuit command received.")
                running = False
                break

        # Poll for CAN messages
        msg = bus.recv(timeout=0.05)
        if msg:
            print(f"Received - ID: {msg.arbitration_id:X}, FD: {msg.is_fd}, DLC: {msg.dlc}, Data: {bytes(msg.data).hex()}")
            
            # Parse all subframes in the message
            data = msg.data
            offset = 0
            
            while offset < len(data):
                # Check if we have enough data left to parse a subframe
                if offset >= len(data):
                    break
                    
                # Get the subframe type
                subframe_type = data[offset]
                offset += 1
                
                # Check if this is a reply subframe (0x20, 0x24, 0x28, 0x2c)
                if (subframe_type & 0xf0) == 0x20:
                    # Extract the data type from the lower bits
                    data_type = (subframe_type & 0x0c) >> 2  # 0=int8, 1=int16, 2=int32, 3=float
                    
                    # Extract number of registers from the lowest 2 bits or the next varuint
                    num_registers = subframe_type & 0x03
                    if num_registers == 0:
                        # If the lowest 2 bits are 0, read a varuint for the number of registers
                        if offset >= len(data):
                            break
                        num_registers = data[offset]
                        offset += 1
                        # Handle multi-byte varuint if MSB is set
                        while offset < len(data) and (data[offset-1] & 0x80) != 0:
                            num_registers = ((num_registers & 0x7f) | ((data[offset] & 0x7f) << 7))
                            offset += 1
                    
                    # Read the start register number as a varuint
                    if offset >= len(data):
                        break
                    start_register = data[offset]
                    offset += 1
                    # Handle multi-byte varuint if MSB is set
                    while offset < len(data) and (data[offset-1] & 0x80) != 0:
                        start_register = ((start_register & 0x7f) | ((data[offset] & 0x7f) << 7))
                        offset += 1
                    
                    print(f"  Subframe: type={subframe_type:02X}, registers={num_registers}, start_reg={start_register:X}")
                    
                    # Process each register value based on data type
                    for i in range(num_registers):
                        if data_type == 0:  # int8
                            if offset >= len(data):
                                break
                            value = data[offset]
                            offset += 1
                            bytes_used = 1
                        elif data_type == 1:  # int16
                            if offset + 1 >= len(data):
                                break
                            value = data[offset] | (data[offset+1] << 8)
                            offset += 2
                            bytes_used = 2
                        elif data_type == 2:  # int32
                            if offset + 3 >= len(data):
                                break
                            value = data[offset] | (data[offset+1] << 8) | (data[offset+2] << 16) | (data[offset+3] << 24)
                            offset += 4
                            bytes_used = 4
                        elif data_type == 3:  # float
                            if offset + 3 >= len(data):
                                break
                            import struct
                            value = struct.unpack('<f', bytes(data[offset:offset+4]))[0]
                            offset += 4
                            bytes_used = 4
                        
                        reg_addr = start_register + i
                        
                        # Handle specific register types we know about
                        if data_type == 0:  # int8
                            if reg_addr == 0x0e:  # Fault code
                                temp_celsius = value * 1.0
                                print(f"    Reg 0x{reg_addr:X}: Board Temperature = {temp_celsius:.1f}°C")
                            elif reg_addr == 0x0a:  # Motor Temperature
                                temp_celsius = value * 1.0
                                print(f"    Reg 0x{reg_addr:X}: Motor Temperature = {temp_celsius:.1f}°C")
                            else:
                                print(f"    Reg 0x{reg_addr:X}: Value = {value} (int8)")
                        elif data_type == 1:  # int16
                            if reg_addr == 0x0e:  # Board Temperature
                                temp_celsius = value * 0.1
                                print(f"    Reg 0x{reg_addr:X}: Board Temperature = {temp_celsius:.1f}°C")
                            elif reg_addr == 0x0a:  # Motor Temperature
                                temp_celsius = value * 0.1
                                print(f"    Reg 0x{reg_addr:X}: Motor Temperature = {temp_celsius:.1f}°C")
                            elif reg_addr == 0x01:  # Position
                                position_rotations = value * 0.0001
                                position_degrees = position_rotations * 360.0
                                print(f"    Reg 0x{reg_addr:X}: Position = {position_rotations:.4f} rotations ({position_degrees:.1f}°)")
                            elif reg_addr == 0x50:  # Aux1 encoder position
                                aux1_rotations = value * 0.0001
                                aux1_degrees = aux1_rotations * 360.0
                                print(f"    Reg 0x{reg_addr:X}: Aux1 Encoder = {aux1_rotations:.4f} rotations ({aux1_degrees:.1f}°)")
                            elif reg_addr >= 0x72 and reg_addr <= 0x74:  # Quaternion components
                                quat_names = ["X", "Y", "Z"]
                                quat_idx = reg_addr - 0x72
                                if quat_idx < len(quat_names):
                                    import struct
                                    quat_val = struct.unpack('<e', struct.pack('<H', value))[0]
                                    print(f"    Reg 0x{reg_addr:X}: Quaternion {quat_names[quat_idx]} = {quat_val:.4f}")
                            else:
                                print(f"    Reg 0x{reg_addr:X}: Value = {value} (raw)")
                        elif data_type == 3:  # float
                            if reg_addr == 0x01:  # Position
                                position_rotations = value  # Float already represents rotations directly
                                position_degrees = position_rotations * 360.0
                                print(f"    Reg 0x{reg_addr:X}: Position = {position_rotations:.6f} rotations ({position_degrees:.3f}°)")
                            elif reg_addr == 0x02:  # Velocity
                                velocity_hz = value  # Float already represents Hz directly
                                velocity_dps = velocity_hz * 360.0
                                print(f"    Reg 0x{reg_addr:X}: Velocity = {velocity_hz:.6f} Hz ({velocity_dps:.3f}°/s)")
                            elif reg_addr == 0x03:  # Torque
                                print(f"    Reg 0x{reg_addr:X}: Torque = {value:.3f} N·m")
                            elif reg_addr == 0x0d:  # Voltage
                                print(f"    Reg 0x{reg_addr:X}: Voltage = {value:.2f} V")
                            elif reg_addr == 0x0e:  # Temperature
                                print(f"    Reg 0x{reg_addr:X}: Temperature = {value:.2f}°C")
                            elif reg_addr == 0x50:  # Aux1 encoder position
                                aux1_degrees = value * 360.0
                                print(f"    Reg 0x{reg_addr:X}: Aux1 Encoder = {value:.6f} rotations ({aux1_degrees:.3f}°)")
                            else:
                                print(f"    Reg 0x{reg_addr:X}: Value = {value} (float)")
                        else:
                            print(f"    Reg 0x{reg_addr:X}: Value = {value} (raw)")
                
                # Handle tunneled stream responses (0x41)
                elif subframe_type == 0x41:
                    if offset + 1 >= len(data):
                        break
                    
                    # Get channel number
                    channel = data[offset]
                    offset += 1
                    
                    # Get data length
                    if offset >= len(data):
                        break
                    data_len = data[offset]
                    offset += 1
                    
                    # Extract the data
                    if offset + data_len > len(data):
                        data_len = len(data) - offset  # Truncate if not enough data
                    
                    if data_len > 0:
                        try:
                            ascii_data = bytes(data[offset:offset+data_len]).decode('ascii')
                            print(f"  Tunneled data (channel {channel}): {ascii_data!r}")
                        except UnicodeDecodeError:
                            print(f"  Tunneled data (channel {channel}): {bytes(data[offset:offset+data_len]).hex()}")
                        
                        offset += data_len
                
                # Handle NOP (0x50)
                elif subframe_type == 0x50:
                    # NOP - just skip this byte
                    print("  NOP padding")
                    continue
                
                # Handle other subframe types
                else:
                    print(f"  Unknown subframe type: 0x{subframe_type:02X}")
                    # Since we don't know the format, we can't safely parse further
                    break
        else:
            # Visual heartbeat so the user knows we're alive
            print(".", end="", flush=True)
            time.sleep(0.05)

except Exception as e:
    print(f"\nError: {e}")

finally:
    # Clean shutdown
    if buses:
        print("\nShutting down buses...")
        for channel_num, bus in buses.items():
            try:
                # Try to cancel all pending transmissions if possible
                if hasattr(bus, 'flush_tx_buffer'):
                    bus.flush_tx_buffer()
            except:
                pass
                
            # Shutdown the bus interface
            bus.shutdown()
            print(f"Bus can{channel_num} shut down.")
            
            # Additional steps to ensure clean shutdown
            try:
                # For SocketCAN specifically, try to bring down the interface
                import os
                print(f"Bringing down CAN interface can{channel_num} via system command...")
                os.system(f"sudo ip link set can{channel_num} down")
                print(f"CAN interface can{channel_num} down.")
            except:
                pass

        # Restore terminal settings
        try:
            termios.tcsetattr(sys.stdin.fileno(), termios.TCSADRAIN, old_term_settings)
        except Exception:
            pass

print("Program terminated cleanly.")
